# LORE-TSR 到 train-anything 迁移开发计划 - 步骤一

## 📋 项目概述

本文档基于需求迭代规划文档(PRD)和详细设计文档(LLD)，制定LORE-TSR项目迁移到train-anything框架的**迭代1第1步**开发计划。严格遵循"小步快跑、持续验证"的开发模式，确保每一步都让项目处于可运行状态。

### 迁移策略回顾
- **复制并保留核心算法**：模型、损失函数、Processor等核心组件逐行复制
- **重构并适配框架入口**：训练循环、配置系统、数据加载完全重构以适配train-anything
- **复制并隔离编译依赖**：DCNv2、NMS等外部依赖独立管理

## 🗺️ 动态迁移蓝图

### 文件迁移映射表和逻辑图

| 源文件 (LORE-TSR) | 目标文件 (train-anything) | 迁移策略 | 当前迭代 | 复杂度 | 状态 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `src/lib/opts.py` | `configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml` | 重构适配：转换为OmegaConf YAML格式 | 迭代1 | **复杂** | `未开始` |
| `src/main.py` | `training_loops/table_structure_recognition/train_lore_tsr.py` | 重构适配：适配accelerate框架 | 迭代1,3 | **复杂** | `未开始` |
| `src/lib/models/model.py` | `networks/lore_tsr/lore_tsr_model.py` | 复制保留：模型工厂函数 | 迭代2 | **复杂** | `未开始` |
| `src/lib/models/losses.py` | `networks/lore_tsr/lore_tsr_loss.py` | 复制保留：逐行复制保持精度 | 迭代4 | 简单 | `未开始` |
| `src/lib/models/classifier.py` | `networks/lore_tsr/processor.py` | 复制保留：Processor组件 | 迭代6 | **复杂** | `未开始` |
| `src/lib/models/transformer.py` | `networks/lore_tsr/transformer.py` | 复制保留：Transformer实现 | 迭代6 | **复杂** | `未开始` |
| `src/lib/models/networks/fpn_resnet_half.py` | `networks/lore_tsr/backbones/fpn_resnet_half.py` | 复制保留：主要骨干网络 | 迭代2 | 简单 | `未开始` |
| `src/lib/datasets/dataset/table_mid.py` | `my_datasets/table_structure_recognition/lore_tsr_dataset.py` | 重构适配：数据集适配器 | 迭代5 | **复杂** | `未开始` |
| `src/lib/utils/post_process.py` | `modules/utils/lore_tsr/post_process.py` | 复制保留：后处理工具 | 迭代11 | 简单 | `未开始` |
| `src/lib/models/networks/DCNv2/` | `external/lore_tsr/DCNv2/` | 复制隔离：可变形卷积 | 迭代7 | 简单 | `未开始` |
| `src/lib/models/networks/NMS/` | `external/lore_tsr/NMS/` | 复制隔离：非极大值抑制 | 迭代7 | 简单 | `未开始` |

### 目标目录结构树

```text
train-anything/
├── configs/table_structure_recognition/lore_tsr/  # [待创建]
│   └── lore_tsr_config.yaml                      # [待创建]
├── training_loops/table_structure_recognition/   # [待创建]
│   └── train_lore_tsr.py                         # [待创建]
├── networks/lore_tsr/                            # [待创建]
│   ├── __init__.py                               # [待创建]
│   ├── lore_tsr_model.py                         # [待创建]
│   ├── lore_tsr_loss.py                          # [待创建]
│   ├── processor.py                              # [待创建]
│   ├── transformer.py                            # [待创建]
│   ├── backbones/                                # [待创建]
│   │   ├── __init__.py                           # [待创建]
│   │   ├── fpn_resnet_half.py                    # [待创建]
│   │   ├── fpn_resnet.py                         # [待创建]
│   │   ├── fpn_mask_resnet_half.py               # [待创建]
│   │   ├── fpn_mask_resnet.py                    # [待创建]
│   │   └── pose_dla_dcn.py                       # [待创建]
│   └── heads/                                    # [待创建]
│       ├── __init__.py                           # [待创建]
│       └── lore_tsr_head.py                      # [待创建]
├── my_datasets/table_structure_recognition/      # [待创建]
│   ├── lore_tsr_dataset.py                       # [待创建]
│   ├── lore_tsr_transforms.py                    # [待创建]
│   └── lore_tsr_target_preparation.py            # [待创建]
├── modules/proj_cmd_args/lore_tsr/               # [待创建]
│   └── args.py                                   # [待创建]
├── modules/utils/lore_tsr/                       # [待创建]
│   ├── __init__.py                               # [待创建]
│   ├── post_process.py                           # [待创建]
│   ├── oracle_utils.py                           # [待创建]
│   └── eval_utils.py                             # [待创建]
├── modules/visualization/                        # [待创建]
│   └── lore_tsr_visualizer.py                    # [待创建]
└── external/lore_tsr/                            # [待创建]
    ├── DCNv2/                                    # [待创建]
    ├── NMS/                                      # [待创建]
    └── cocoapi/                                  # [待创建]
```

## 🎯 迭代1第1步：创建基础目录结构

### 步骤标题
**迭代1步骤1.1: 创建基础目录结构和模块初始化**

### 当前迭代
**迭代1：基础目录结构和配置系统**

### 影响文件
本步骤将创建以下文件和目录：
- `configs/table_structure_recognition/lore_tsr/` 目录
- `training_loops/table_structure_recognition/` 目录  
- `networks/lore_tsr/` 完整目录结构
- `my_datasets/table_structure_recognition/` 目录
- `modules/proj_cmd_args/lore_tsr/` 目录
- `modules/utils/lore_tsr/` 目录
- `external/lore_tsr/` 目录
- 所有必要的 `__init__.py` 文件

### 具体操作

#### 1. 创建配置目录结构
```bash
# 创建配置目录
mkdir -p configs/table_structure_recognition/lore_tsr
```

#### 2. 创建训练循环目录
```bash
# 创建训练循环目录
mkdir -p training_loops/table_structure_recognition
```

#### 3. 创建网络模块完整目录结构
```bash
# 创建网络模块目录
mkdir -p networks/lore_tsr/backbones
mkdir -p networks/lore_tsr/heads
```

#### 4. 创建数据集目录
```bash
# 创建数据集目录
mkdir -p my_datasets/table_structure_recognition
```

#### 5. 创建工具模块目录
```bash
# 创建命令行参数模块目录
mkdir -p modules/proj_cmd_args/lore_tsr

# 创建工具函数目录
mkdir -p modules/utils/lore_tsr
```

#### 6. 创建外部依赖目录
```bash
# 创建外部依赖目录
mkdir -p external/lore_tsr
```

#### 7. 创建所有__init__.py文件
每个Python包目录都需要创建__init__.py文件，内容包含版本信息和基础导出接口。

### 受影响的现有模块
- **无影响**：本步骤只创建新目录和文件，不修改train-anything现有模块
- **兼容性**：所有新增目录都在独立命名空间中，确保与现有功能完全兼容

### 复用已有代码
- 参考 `networks/cycle_centernet_ms/` 的目录结构设计
- 复用 `modules/proj_cmd_args/cycle_centernet/` 的模块组织方式
- 遵循 train-anything 框架的标准目录命名规范

### 如何验证 (Verification)

#### 验证命令1：检查目录结构完整性
```bash
# 验证所有目录都已创建
find . -name "lore_tsr" -type d
find . -path "*/table_structure_recognition" -type d
find . -path "*/external/lore_tsr" -type d
```

#### 验证命令2：测试Python模块导入
```bash
# 测试基础模块导入
python -c "import networks.lore_tsr; print('networks.lore_tsr 导入成功')"
python -c "import networks.lore_tsr.backbones; print('backbones 模块导入成功')"
python -c "import networks.lore_tsr.heads; print('heads 模块导入成功')"
python -c "import modules.proj_cmd_args.lore_tsr; print('args 模块导入成功')"
python -c "import modules.utils.lore_tsr; print('utils 模块导入成功')"
```

#### 验证命令3：确认无ImportError
```bash
# 运行Python解释器测试导入
python -c "
try:
    import networks.lore_tsr
    import my_datasets.table_structure_recognition
    import modules.proj_cmd_args.lore_tsr
    import modules.utils.lore_tsr
    print('✅ 所有模块导入测试通过')
except ImportError as e:
    print(f'❌ 导入错误: {e}')
    exit(1)
"
```

### 当前迭代逻辑图

```mermaid
graph TD
    %% 当前迭代：迭代1步骤1.1 - 创建基础目录结构

    subgraph "目标：train-anything基础架构"
        direction TB
        T1["configs/table_structure_recognition/lore_tsr/"]
        T2["training_loops/table_structure_recognition/"]
        T3["networks/lore_tsr/"]
        T4["my_datasets/table_structure_recognition/"]
        T5["modules/proj_cmd_args/lore_tsr/"]
        T6["modules/utils/lore_tsr/"]
        T7["external/lore_tsr/"]
    end

    subgraph "创建操作"
        direction LR
        OP1["mkdir -p 创建目录"]
        OP2["创建 __init__.py 文件"]
        OP3["设置模块导出接口"]
    end

    subgraph "验证检查"
        direction LR
        V1["目录结构完整性检查"]
        V2["Python模块导入测试"]
        V3["ImportError检查"]
    end

    %% 操作流程
    OP1 --> T1
    OP1 --> T2
    OP1 --> T3
    OP1 --> T4
    OP1 --> T5
    OP1 --> T6
    OP1 --> T7

    OP2 --> T3
    OP2 --> T5
    OP2 --> T6

    OP3 --> T3
    OP3 --> T5
    OP3 --> T6

    %% 验证流程
    T1 --> V1
    T2 --> V1
    T3 --> V2
    T4 --> V2
    T5 --> V2
    T6 --> V2
    T7 --> V1

    V2 --> V3
```

## 📝 步骤完成标准

### 功能验收
1. **目录结构完整**：所有预定义目录都已创建，符合train-anything规范
2. **模块导入成功**：所有Python包都能正常导入，无ImportError
3. **__init__.py完整**：每个包目录都有适当的初始化文件
4. **命名规范正确**：所有目录和文件命名符合框架标准

### 技术验收
1. **代码质量**：所有__init__.py文件符合Python代码规范
2. **文档完整性**：每个文件都有清晰的文档说明和用途描述
3. **扩展性验证**：目录结构为后续迭代预留了正确的扩展点
4. **兼容性确认**：不影响train-anything现有功能的正常运行

### 集成验收
1. **框架集成**：完全符合train-anything的目录结构和命名规范
2. **依赖管理**：所有依赖都正确声明，无隐式依赖
3. **版本控制**：所有文件都有适当的版本标识和变更记录

## 🚨 风险管理

### 技术风险
1. **目录权限问题**：确保有足够权限创建目录和文件
2. **路径冲突**：检查是否与现有目录冲突
3. **导入路径问题**：验证Python能正确识别新创建的包

### 缓解措施
1. **权限检查**：执行前检查目录写权限
2. **冲突检测**：创建前检查目标路径是否已存在
3. **导入验证**：每个步骤后立即验证模块导入

## 📋 下一步预告

**迭代1步骤1.2**：创建配置文件和参数解析
- 将LORE-TSR的opts.py转换为OmegaConf YAML格式
- 创建命令行参数解析模块
- 验证配置文件能被OmegaConf正确解析

---

**文档版本**：v1.0  
**创建日期**：2025-07-18  
**当前迭代**：迭代1（基础目录结构和配置系统）  
**当前步骤**：步骤1.1（创建基础目录结构）  
**验证要求**：项目可运行、模块可导入、无ImportError
